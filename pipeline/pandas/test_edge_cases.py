#!/usr/bin/env python3

import numpy as np
from_numpy = __import__('0-from_numpy').from_numpy

# Test with 1D array
print("Testing 1D array:")
arr_1d = np.array([1, 2, 3, 4, 5])
try:
    df_1d = from_numpy(arr_1d.reshape(-1, 1))  # Reshape to 2D
    print(df_1d)
except Exception as e:
    print(f"Error with 1D array: {e}")

# Test with single column
print("\nTesting single column:")
arr_single = np.array([[1], [2], [3]])
df_single = from_numpy(arr_single)
print(df_single)

# Test with maximum columns (26)
print("\nTesting with many columns (10):")
arr_many = np.random.randn(3, 10)
df_many = from_numpy(arr_many)
print(df_many.columns.tolist())
print(df_many)
