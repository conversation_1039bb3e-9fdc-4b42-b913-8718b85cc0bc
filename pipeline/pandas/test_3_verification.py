#!/usr/bin/env python3

from_file = __import__('2-from_file').from_file
rename = __import__('3-rename').rename

# Load the original data
df_original = from_file('coinbaseUSD_1-min_data_2014-12-01_to_2019-01-09.csv', ',')
print("Original DataFrame columns:")
print(df_original.columns.tolist())
print("\nOriginal DataFrame head (first 3 rows):")
print(df_original.head(3))

# Apply the rename function
df_renamed = rename(df_original)
print("\nRenamed DataFrame columns:")
print(df_renamed.columns.tolist())
print("\nRenamed DataFrame head (first 3 rows):")
print(df_renamed.head(3))
print("\nRenamed DataFrame tail (last 3 rows):")
print(df_renamed.tail(3))

# Check data types
print("\nData types:")
print(df_renamed.dtypes)
